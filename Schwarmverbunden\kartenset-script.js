// Kartenset JavaScript für Schwarmverbunden

document.addEventListener('DOMContentLoaded', function() {
    // Loader anzeigen
    const loader = document.getElementById('loader');
    loader.style.display = 'block';
    
    // Fragen für jede Kategorie
    const questions = {
        tag1: [
            {
                text: "Was ist das größte ungestillte Bedürfnis in deinem Leben?",
                placeholder: "assets/bilder/Fragen/1.png"
            },
            {
                text: "Wenn über Nacht ein Wunder passiert und du findest dich morgen in deinem absoluten Traumleben wieder, was wäre dann anders?",
                placeholder: "assets/bilder/Fragen/2.png"
            },
            {
                text: "Gibt es aktuelle globale Herausforderungen, die dir besonders Sorgen bereiten?",
                placeholder: "assets/bilder/Fragen/3.png"
            },
            {
                text: "Gibt es gesellschaftliche oder soziale Themen, die dir besonders wichtig sind?",
                placeholder: "assets/bilder/Fragen/4.png"
            },
            {
                text: "Welche Werte sind dir wertvoll und wichtig im Leben?",
                placeholder: "assets/bilder/Fragen/5.png"
            },
            {
                text: "Was bringt dich im Leben am meisten zum Strahlen/Erblühen?",
                placeholder: "assets/bilder/Fragen/6.png"
            },
            {
                text: "Welche Herausforderungen beschäftigen dich im Moment in deinem Leben am meisten?",
                placeholder: "assets/bilder/Fragen/7.png"
            },
            {
                text: "Hast du schon mal \"spirituelle Impulse\" zu deiner Vision bekommen (z.B. über dein höheres Selbst, ein Medium, dein Geburtshoroskop o.ä.)?",
                placeholder: "assets/bilder/Fragen/8.png"
            },
            {
                text: "Welcher Vision würdest du nachgehen, wenn du unendlich viel Selbstvertrauen und Selbstbewusstsein hättest und dich nichts blockieren würde?",
                placeholder: "assets/bilder/Fragen/9.png"
            },
            {
                text: "Gibt es ein Thema, das dich seit Jahren immer wieder ruft?",
                placeholder: "assets/bilder/Fragen/10.png"
            },
            {
                text: "Was bedeutet Erfolg für dich ganz persönlich?",
                placeholder: "assets/bilder/Fragen/11.png"
            }
        ],
        tag2: [
            {
                text: "Welche großen Herausforderungen haben dein Leben geprägt und was hast du dabei gelernt?",
                placeholder: "assets/bilder/Fragen/12.png"
            },
            {
                text: "Gibt es Tätigkeiten, die du lange ausgeübt hast, die du auf keinen Fall wieder machen möchtest?",
                placeholder: "assets/bilder/Fragen/13.png"
            },
            {
                text: "Was schätzt du an dir selbst, das andere oft übersehen?",
                placeholder: "assets/bilder/Fragen/14.png"
            },
            {
                text: "Bist du eher ein rationaler Kopfmensch, ein intuitiver Gefühlsmensch oder eher instinktiv-körperlich?",
                placeholder: "assets/bilder/Fragen/15.png"
            },
            {
                text: "Kennst du deinen Persönlichkeitstyp (z.B. Human Design, 16 Personalities o.ä.) und was macht diesen aus?",
                placeholder: "assets/bilder/Fragen/16.png"
            },
            {
                text: "Was würdest du gerne können, das du an anderen bewunderst?",
                placeholder: "assets/bilder/Fragen/17.png"
            },
            {
                text: "Wenn Geld keine Rolle spielen würde und alles erfolgreich wäre, egal wie schräg die Idee ist, womit würdest du dann am liebsten deine Zeit verbringen?",
                placeholder: "assets/bilder/Fragen/18.png"
            },
            {
                text: "Wo in deinem Leben hast du schon mal jemanden tief inspiriert?",
                placeholder: "assets/bilder/Fragen/19.png"
            },
            {
                text: "Hast du Gaben, die noch \"roh\" sind und wachsen wollen, die du gerne mal hier in diesem sicheren Rahmen ausprobieren möchtest?",
                placeholder: "assets/bilder/Fragen/20.png"
            },
            {
                text: "Was machst du regelmäßig, das für dich normal ist, und für andere vielleicht schwer wäre?",
                placeholder: "assets/bilder/Fragen/21.png"
            }
        ],
        tag3: [
            {
                text: "Was möchten wir über die nächsten Tage gemeinsam co-kreieren (Ziel)?",
                placeholder: "assets/bilder/Fragen/22.png"
            },
            {
                text: "Gibt es ein ungestilltes Bedürfnis in der Gruppe, das wir gemeinsam stillen möchten?",
                placeholder: "assets/bilder/Fragen/24.png"
            },
            {
                text: "Gibt es ein kreatives Projekt, das wir umsetzen möchten?",
                placeholder: "assets/bilder/Fragen/25.png"
            },
            {
                text: "Was ist unser \"gemeinsamer Ruf\"?",
                placeholder: "assets/bilder/Fragen/26.png"
            },
            {
                text: "Wo überschneiden sich unsere Visionen auf kraftvolle Weise?",
                placeholder: "assets/bilder/Fragen/27.png"
            },
            {
                text: "Welche Fähigkeiten stehen uns als Gruppe zur Verfügung und wie können wir sie schlau einsetzen?",
                placeholder: "assets/bilder/Fragen/28.png"
            },
            {
                text: "Wer von uns hat einen Platz in welcher Geniezone?",
                placeholder: "assets/bilder/Fragen/29.png"
            },
            {
                text: "Wie fühlt sich unsere gemeinsame Essenz an, wenn wir sie nur mit einem Wort beschreiben?",
                placeholder: "assets/bilder/Fragen/30.png"
            },
            {
                text: "Gibt es einen Titel oder Satz, der unsere gemeinsame Ausrichtung beschreibt?",
                placeholder: "assets/bilder/Fragen/31.png"
            },
            {
                text: "Was haben wir gemeinsam und wo unterscheiden wir uns?",
                placeholder: "assets/bilder/Fragen/32.png"
            },
            {
                text: "Sind wir als Gruppe zueinander kompatibel (hinsichtlich unserer Werte, Visionen, Lebensvorstellungen)?",
                placeholder: "assets/bilder/Fragen/33.png"
            }
        ],
        tag4: [
            {
                text: "Welche Schwierigkeiten hast du generell in Gruppen und ist das in dieser Gruppe schon aufgetreten?",
                placeholder: "assets/bilder/Fragen/34.png"
            },
            {
                text: "Wo waren wir die letzten Tage irgendwie nicht so richtig im Flow und woran könnte es gelegen haben?",
                placeholder: "assets/bilder/Fragen/35.png"
            },
            {
                text: "Gibt es Momente, in denen du dich nicht so richtig verstanden gefühlt hast, und möchtest du deine Perspektive noch einmal ganz ausführlich erklären?",
                placeholder: "assets/bilder/Fragen/36.png"
            },
            {
                text: "Welche Hürden könnten dem Erreichen unseres Ziels im Wege stehen?",
                placeholder: "assets/bilder/Fragen/37.png"
            },
            {
                text: "Welche (noch unausgesprochenen) Erwartungen hast du an die Gruppe oder unser Projekt?",
                placeholder: "assets/bilder/Fragen/38.png"
            },
            {
                text: "Welche gesellschaftlichen Rollen und Erwartungen engen dich ein?",
                placeholder: "assets/bilder/Fragen/39.png"
            },
            {
                text: "Was in uns darf sich verändern, damit dieses Projekt gelingen kann?",
                placeholder: "assets/bilder/Fragen/40.png"
            },
            {
                text: "Gibt es etwas, das du ansprechen/aussprechen möchtest, aber dich bisher nicht getraut hast?",
                placeholder: "assets/bilder/Fragen/41.png"
            },
            {
                text: "Gibt es Gefühle, die du bisher zurückgehalten hast, denen wir gemeinsam Raum geben können?",
                placeholder: "assets/bilder/Fragen/42.png"
            },
            {
                text: "In welchen Momenten hast du dich in der Gruppe besonders verbunden gefühlt?",
                placeholder: "assets/bilder/Fragen/43.png"
            },
            {
                text: "Gibt es etwas, das wir tun können, damit du dich noch sicherer/freier fühlen kannst, du selbst zu sein?",
                placeholder: "assets/bilder/Fragen/44.png"
            },
            {
                text: "Worin möchtest du der Gruppe (noch) mehr vertrauen?",
                placeholder: "assets/bilder/Fragen/45.png"
            },
            {
                text: "Was brauchst du, um dich noch mehr einlassen zu können?",
                placeholder: "assets/bilder/Fragen/46.png"
            }
        ],
        tag5: [
            {
                text: "Wie wollen wir jetzt vorgehen?",
                placeholder: "assets/bilder/Fragen/47.png"
            },
            {
                text: "Auf welche Weise könnten wir unser Ziel angehen, die besonders viel Spaß macht?",
                placeholder: "assets/bilder/Fragen/48.png"
            },
            {
                text: "Angenommen, jeder macht nur das, was leicht fällt, was würde dann passieren?",
                placeholder: "assets/bilder/Fragen/49.png"
            },
            {
                text: "Gibt es innere Impulse, die wir spüren, denen wir nachgehen könnten?",
                placeholder: "assets/bilder/Fragen/50.png"
            },
            {
                text: "Welche Strukturen würden unserem Schwarm gut tun?",
                placeholder: "assets/bilder/Fragen/51.png"
            }
        ],
        tag6: [
            {
                text: "Gibt es etwas, das wir heute anders machen möchten, als gestern?",
                placeholder: "assets/bilder/Fragen/52.png"
            },
            {
                text: "Haben wir bisher eine Schwarmintelligenz wahrgenommen? Wie können wir darauf stärker zugreifen?",
                placeholder: "assets/bilder/Fragen/53.png"
            },
            {
                text: "Welche Richtung würde unser Projekt heute nehmen, wenn wir etwas Mutiges tun würden?",
                placeholder: "assets/bilder/Fragen/54.png"
            }
        ],
        tag7: [
            {
                text: "Wie gut ist es uns gelungen, zu co-kreieren?",
                placeholder: "assets/bilder/Fragen/55.png"
            },
            {
                text: "Wie hat sich die Gruppendynamik über die Zeit entwickelt?",
                placeholder: "assets/bilder/Fragen/56.png"
            },
            {
                text: "Was würdest du nächstes Mal anders machen?",
                placeholder: "assets/bilder/Fragen/57.png"
            },
            {
                text: "Was braucht es noch, um mit dem Projekt einen guten Abschluss zu finden?",
                placeholder: "assets/bilder/Fragen/58.png"
            },
            {
                text: "Wollen wir das Projekt auch nach dem Retreat weiterführen und was braucht es dafür noch?",
                placeholder: "assets/bilder/Fragen/59.png"
            },
            {
                text: "Wie hast du dich die letzten Tage gefühlt?",
                placeholder: "assets/bilder/Fragen/60.png"
            },
            {
                text: "Was war dein größter innerer Shift über die Woche?",
                placeholder: "assets/bilder/Fragen/61.png"
            },
            {
                text: "Was möchtest du aus dieser Erfahrung in dein Leben mitnehmen?",
                placeholder: "assets/bilder/Fragen/62.png"
            },
            {
                text: "Wofür bist du dankbar?",
                placeholder: "assets/bilder/Fragen/55.png"
            },
            {
                text: "Was möchtest du der Gruppe gerne rückmelden?",
                placeholder: "assets/bilder/Fragen/56.png"
            },
            {
                text: "Gibt es etwas, das noch ausgesprochen werden möchte?",
                placeholder: "assets/bilder/Fragen/57.png"
            }
        ]
    };

    // DOM-Elemente
    const decks = document.querySelectorAll('.deck');
    const cardModalWrapper = document.getElementById('card-modal-wrapper');
    const closeModalBtn = document.getElementById('close-modal');
    const drawNewCardBtn = document.getElementById('draw-new-card');
    const cardQuestionText = document.getElementById('card-question-text');
    const cardPlaceholder = document.getElementById('card-placeholder');
    const cardCategoryLabel = document.getElementById('card-category-label');
    const modalCard = document.querySelector('.modal-card');

    let activeCategory = null;
    let currentDeck = null;

    // Kategorie-Labels
    const categoryLabels = {
        tag1: "Tag 1: Den Ruf erkunden",
        tag2: "Tag 2: Das Licht erkunden",
        tag3: "Tag 3: Das gemeinsame Puzzle",
        tag4: "Tag 4: Gruppendynamiken lösen",
        tag5: "Tag 5: Zum Schwarm werden",
        tag6: "Tag 6: Im Flow",
        tag7: "Tag 7: Abschluss & Reflexion"
    };

    // Funktion um eine zufällige Karte zu ziehen mit Animation
    function drawCard(category, deck) {
        // Lade-Animation anzeigen
        loader.style.display = 'block';

        // Aktive Kategorie und Deck speichern
        activeCategory = category;
        currentDeck = deck;

        // Deck-Position für Animation speichern
        let deckRect = null;
        if (deck) {
            deckRect = deck.getBoundingClientRect();
        }

        setTimeout(() => {
            // Zufällige Frage aus der Kategorie wählen
            const categoryQuestions = questions[category];
            const randomQuestion = categoryQuestions[Math.floor(Math.random() * categoryQuestions.length)];

            // Modal-Inhalte setzen
            cardQuestionText.textContent = randomQuestion.text;
            cardPlaceholder.src = randomQuestion.placeholder;
            cardCategoryLabel.textContent = categoryLabels[category];

            // Modal anzeigen
            cardModalWrapper.classList.add('active');

            // Karten-Zieh-Animation
            if (deckRect) {
                // Setze die Modalcard für die Animation zurück
                modalCard.style.animation = 'none';
                modalCard.style.opacity = '0';
                modalCard.style.transform = 'scale(0.6) translateY(40px) rotate(-5deg)';

                // Kleine Verzögerung für visuelle Effekte
                setTimeout(() => {
                    // Animation starten
                    modalCard.style.animation = 'floatIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';
                }, 100);
            }

            // Lade-Animation ausblenden
            loader.style.display = 'none';
        }, 300);
    }

    // Event-Listener für Kartenstapel
    decks.forEach(deck => {
        deck.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            drawCard(category, this);
        });
    });

    // Event-Listener für Modal schließen
    closeModalBtn.addEventListener('click', function() {
        cardModalWrapper.classList.remove('active');
        setTimeout(() => {
            activeCategory = null;
            currentDeck = null;
        }, 300);
    });

    // Event-Listener für "Neue Karte ziehen"
    drawNewCardBtn.addEventListener('click', function() {
        if (activeCategory) {
            // Für die neue Karte eine kleine Animation
            modalCard.style.animation = 'none';
            modalCard.offsetHeight; // Reflow
            modalCard.style.animation = 'floatIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';

            // Neue Karte aus der gleichen Kategorie ziehen
            drawCard(activeCategory, currentDeck);
        }
    });

    // ESC-Taste zum Schließen
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && cardModalWrapper.classList.contains('active')) {
            cardModalWrapper.classList.remove('active');
            setTimeout(() => {
                activeCategory = null;
                currentDeck = null;
            }, 300);
        }
    });

    // Modal-Wrapper Click zum Schließen
    cardModalWrapper.addEventListener('click', function(e) {
        if (e.target === cardModalWrapper) {
            cardModalWrapper.classList.remove('active');
            setTimeout(() => {
                activeCategory = null;
                currentDeck = null;
            }, 300);
        }
    });

    // Bilder vorladen
    function preloadImages() {
        const imageUrls = [];
        // Alle Bilder von 1-62 (außer 23) hinzufügen
        for (let i = 1; i <= 62; i++) {
            if (i !== 23) { // 23.png fehlt
                imageUrls.push(`assets/bilder/Fragen/${i}.png`);
            }
        }

        const imagePromises = imageUrls.map(url => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(url);
                img.onerror = () => {
                    console.warn(`Konnte Bild nicht laden: ${url}`);
                    resolve(url); // Trotzdem auflösen, damit der Prozess weitergeht
                };
                img.src = url;
            });
        });

        return Promise.all(imagePromises);
    }

    // Initialisierung
    preloadImages().then(() => {
        // Loader ausblenden nach dem Laden
        setTimeout(() => {
            loader.style.display = 'none';
        }, 500);
    });
});
