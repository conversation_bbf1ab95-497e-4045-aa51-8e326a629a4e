/* <PERSON>rten<PERSON> Styles für Schwarmverbunden */

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Lora', serif;
    -webkit-tap-highlight-color: transparent;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    color: #333;
    overflow-x: hidden;
}

/* Header und Footer */
.header {
    background-color: #04070f;
    color: #29303b;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    height: 60px;
    font-family: 'Montserrat', sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    width: 100%;
    box-sizing: border-box;
}

.header-link {
    color: #29303b;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 300;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    transition: color 0.3s ease;
}

.header-link:hover {
    color: #f59034;
}

/* Hamburger Menu */
.hamburger-menu {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 24px;
    z-index: 1003;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background-color: #29303b;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.hamburger-menu:hover .hamburger-line {
    background-color: #f59034;
}

/* Navigation Menu */
.nav-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background-color: #29303b;
    z-index: 1001;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

.nav-menu.active {
    right: 0;
}

.nav-menu-content {
    padding: 80px 0 0 0;
}

.nav-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-links li {
    border-bottom: 1px solid rgba(251, 209, 154, 0.1);
}

.nav-link {
    display: block;
    padding: 20px 30px;
    color: #fbd19a;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: #fbd19a;
    color: #29303b;
    border-left-color: #fbd19a;
    transform: translateX(5px);
}

/* Overlay for menu */
.nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: transparent;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

.footer {
    background-color: #04070f;
    padding: 20px 0;
    text-align: center;
    margin-top: auto;
}

.footer p {
    font-family: 'Montserrat', sans-serif;
    color: #fbd19a;
    font-size: 14px;
    margin: 0;
}

.footer a {
    color: #fbd19a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: #f59034;
}

/* Container */
.container {
    flex: 1;
    padding: 100px 20px 60px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}


    padding: 0 10px;
}

/* Kartenstapel-Bereich */
.card-decks {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.deck {
    position: relative;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    perspective: 1000px;
}

.deck:hover {
    transform: translateY(-8px);
}



.cards-container {
    position: relative;
    height: 200px;
    perspective: 1200px;
}

.card {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    cursor: pointer;
}

/* Anpassung für besseren Stapel-Effekt */
.card.stacked:nth-child(1) { transform: translateZ(0) translateX(0) translateY(0); }
.card.stacked:nth-child(2) { transform: translateZ(-1px) translateX(2px) translateY(2px); }
.card.stacked:nth-child(3) { transform: translateZ(-2px) translateX(4px) translateY(4px); }
.card.stacked:nth-child(4) { transform: translateZ(-3px) translateX(6px) translateY(6px); opacity: 0.9; }
.card.stacked:nth-child(5) { transform: translateZ(-4px) translateX(8px) translateY(8px); opacity: 0.8; }

/* Deck-Hover-Effekt mit verbesserter Animation */
.deck:hover .card.stacked:nth-child(1) { 
    transform: translateZ(5px) translateX(0) translateY(-5px); 
    box-shadow: 0 12px 24px rgba(251, 209, 154, 0.2); 
}

.deck:hover .card.stacked:nth-child(2) { 
    transform: translateZ(3px) translateX(3px) translateY(0px); 
}

.deck:hover .card.stacked:nth-child(3) { 
    transform: translateZ(1px) translateX(6px) translateY(2px); 
}

.card-back {
    width: 100%;
    height: 100%;
    background: #29303b;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid rgba(251, 209, 154, 0.3);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.deck-label {
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #fbd19a;
    text-align: center;
    line-height: 1.3;
    padding: 15px;
}

/* Modal Styles */
.card-modal-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #29303b, #38160e);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.card-modal-wrapper.active {
    opacity: 1;
    visibility: visible;
}

.close-modal {
    position: absolute;
    top: 30px;
    right: 30px;
    background: none;
    border: none;
    color: #fbd19a;
    font-size: 36px;
    cursor: pointer;
    z-index: 1001;
    transition: color 0.3s ease;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    color: #f59034;
}

.card-category-label {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #fbd19a;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-card {
    width: 350px;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: floatIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.card-placeholder-container {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    overflow: hidden;
}

.card-placeholder {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px 20px 0 0;
}

.card-content {
    background: #fff;
    padding: 30px 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 200px;
    border-radius: 0 0 20px 20px;
}

.card-question-text {
    font-size: 18px;
    line-height: 1.6;
    color: #333;
    margin: 0;
    font-weight: 400;
}

.draw-new-card {
    background: linear-gradient(135deg, #f59034, #b44355);
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 30px;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.draw-new-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 144, 52, 0.4);
}

/* Animationen */
@keyframes floatIn {
    0% {
        opacity: 0;
        transform: scale(0.6) translateY(40px) rotate(-5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0) rotate(0deg);
    }
}

/* Loading Animation */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 4px solid rgba(251, 209, 154, 0.3);
    border-top: 4px solid #fbd19a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2000;
    display: none;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Deck Animationen */
.deck {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.deck:nth-child(1) { animation-delay: 0.1s; }
.deck:nth-child(2) { animation-delay: 0.2s; }
.deck:nth-child(3) { animation-delay: 0.3s; }
.deck:nth-child(4) { animation-delay: 0.4s; }
.deck:nth-child(5) { animation-delay: 0.5s; }
.deck:nth-child(6) { animation-delay: 0.6s; }
.deck:nth-child(7) { animation-delay: 0.7s; }

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .container {
        padding: 20px 15px 40px;
    }

    .card-decks {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 25px;
    }

    .cards-container {
        height: 224px;
    }

    .main-title {
        font-size: 24px;
    }

    .modal-card {
        width: 300px;
    }

    .card-placeholder-container {
        height: 200px;
    }

    .card-question-text {
        font-size: 16px;
    }

    .intro-text {
        font-size: 15px;
    }

    .draw-new-card {
        padding: 10px 20px;
        font-size: 14px;
    }

    .intro-section {
        padding: 25px 15px;
        margin-bottom: 30px;
    }

    .close-modal {
        top: 20px;
        right: 20px;
        font-size: 30px;
    }
}

@media (max-width: 480px) {
    .card-decks {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .cards-container {
        height: 180px;
    }

    .modal-card {
        width: 280px;
        height: 400px;
    }

    .deck-title {
        font-size: 14px;
        padding-bottom: 8px;
        margin-bottom: 10px;
    }

    .main-title {
        font-size: 20px;
    }

    .card-question-text {
        font-size: 14px;
    }

    .card-placeholder-container {
        height: 140px;
    }

    .card-placeholder {
        width: 80px;
        height: 80px;
    }

    .card-content {
        min-height: 200px;
        padding: 20px 15px;
    }

    .header {
        padding: 0 20px;
    }

    .container {
        padding: 80px 15px 40px;
    }

    .card-decks {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 0 10px;
    }

    .cards-container {
        height: 160px;
    }

    .deck-label {
        font-size: 12px;
        padding: 10px;
    }

    .hamburger-line {
        background-color: #29303b;
    }
}
