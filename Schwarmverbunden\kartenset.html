<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retreat Kartenset - Schwarmverbunden</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Kartenset Styles -->
    <link rel="stylesheet" href="kartenset-styles.css">
</head>

<body>
    <div class="loading-indicator" id="loader"></div>

    <header class="header">
        <a href="index.html" class="header-link">SCHWARMVERBUNDEN</a>
        <button class="hamburger-menu" id="hamburgerMenu" aria-label="Menü öffnen">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>
    </header>

    <!-- Navigation Menu -->
    <nav class="nav-menu" id="navMenu">
        <div class="nav-menu-content">
            <ul class="nav-links">
                <li><a href="index.html#event" class="nav-link">CO-KREATIVES RETREAT</a></li>
                <li><a href="index.html#jana" class="nav-link">ÜBER MICH</a></li>
                <li><a href="impressum.html" class="nav-link">IMPRESSUM & DATENSCHUTZ</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">

        <div class="card-decks">
            <!-- Tag 1: Den Ruf erkunden -->
            <div class="deck deck-tag1" data-category="tag1">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 1: Den Ruf erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 2: Das Licht erkunden -->
            <div class="deck deck-tag2" data-category="tag2">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 2: Das Licht erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 3: Das gemeinsame Puzzle -->
            <div class="deck deck-tag3" data-category="tag3">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 3: Das gemeinsame Puzzle</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 4: Gruppendynamiken lösen -->
            <div class="deck deck-tag4" data-category="tag4">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 4: Gruppendynamiken lösen</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 5: Zum Schwarm werden -->
            <div class="deck deck-tag5" data-category="tag5">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 5: Zum Schwarm werden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 6: Im Flow -->
            <div class="deck deck-tag6" data-category="tag6">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 6: Im Flow</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Tag 7: Abschluss & Reflexion -->
            <div class="deck deck-tag7" data-category="tag7">
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="deck-label">Tag 7: Abschluss & Reflexion</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karten-Modal -->
    <div class="card-modal-wrapper" id="card-modal-wrapper">
        <button class="close-modal" id="close-modal">&times;</button>

        <!-- Kategorie ÜBER dem Modal -->
        <span class="card-category-label" id="card-category-label"></span>

        <div class="modal-card">
            <div class="card-placeholder-container">
                <img src="assets/bilder/Fragen/1.png" alt="" class="card-placeholder" id="card-placeholder">
            </div>
            <div class="card-content">
                <p class="card-question-text" id="card-question-text"></p>
            </div>
        </div>

        <!-- Button bleibt unten -->
        <button class="draw-new-card" id="draw-new-card">Neue Karte ziehen</button>
    </div>

    <footer class="footer">
        <p>© 2025 <a href="index.html">Schwarmverbunden</a> | <a href="impressum.html">Impressum & Datenschutz</a></p>
    </footer>

    <!-- Navigation Overlay -->
    <div class="nav-overlay" id="navOverlay"></div>

    <!-- Kartenset JavaScript -->
    <script src="kartenset-script.js"></script>

    <!-- Navigation JavaScript -->
    <script>
        // Hamburger Menu Functionality
        const hamburgerMenu = document.getElementById('hamburgerMenu');
        const navMenu = document.getElementById('navMenu');
        const navOverlay = document.getElementById('navOverlay');

        function openMenu() {
            navMenu.classList.add('active');
            navOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeMenu() {
            navMenu.classList.remove('active');
            navOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        hamburgerMenu.addEventListener('click', () => {
            if (navMenu.classList.contains('active')) {
                closeMenu();
            } else {
                openMenu();
            }
        });

        navOverlay.addEventListener('click', closeMenu);

        // Close menu when clicking anywhere outside
        document.addEventListener('click', (event) => {
            if (navMenu.classList.contains('active')) {
                if (!navMenu.contains(event.target) && !hamburgerMenu.contains(event.target)) {
                    closeMenu();
                }
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && navMenu.classList.contains('active')) {
                closeMenu();
            }
        });
    </script>
</body>
</html>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/47.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/47.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/47.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/47.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 6: Im Flow -->
            <div class="deck deck-tag6" data-category="tag6">
                <h2 class="deck-title">Tag 6: Im Flow</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/52.png" alt="Tag 6" class="card-cover-img">
                            </div>
                            <div class="deck-label">Im Flow</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/52.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/52.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/52.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/52.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 7: Abschluss und Reflexion -->
            <div class="deck deck-tag7" data-category="tag7">
                <h2 class="deck-title">Tag 7: Abschluss & Reflexion</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/55.png" alt="Tag 7" class="card-cover-img">
                            </div>
                            <div class="deck-label">Abschluss & Reflexion</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/55.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/55.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/55.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/55.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karten-Modal -->
    <div class="card-modal-wrapper" id="card-modal-wrapper">
        <button class="close-modal" id="close-modal">&times;</button>

        <!-- Kategorie ÜBER dem Modal -->
        <span class="card-category-label" id="card-category-label"></span>

        <div class="modal-card">
            <div class="card-placeholder-container">
                <img src="assets/bilder/Fragen/1.png" alt="" class="card-placeholder" id="card-placeholder">
            </div>
            <div class="card-content">
                <p class="card-question-text" id="card-question-text"></p>
            </div>
        </div>

        <!-- Button bleibt unten -->
        <button class="draw-new-card" id="draw-new-card">Neue Karte ziehen</button>
    </div>

    <footer class="footer">
        <p>© 2025 <a href="index.html">Schwarmverbunden</a> | <a href="impressum.html">Impressum & Datenschutz</a></p>
    </footer>

    <!-- Kartenset JavaScript -->
    <script src="kartenset-script.js"></script>
</body>
</html>
