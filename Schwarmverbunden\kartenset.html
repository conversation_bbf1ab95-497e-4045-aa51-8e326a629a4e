<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retreat Kartenset - Schwarmverbunden</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Kartenset Styles -->
    <link rel="stylesheet" href="kartenset-styles.css">
</head>

<body>
    <div class="loading-indicator" id="loader"></div>

    <header class="header">
        <a href="index.html" class="header-link">Schwarmverbunden</a>
    </header>

    <div class="container">
        <div class="intro-section">
            <h1 class="main-title">Retreat Kartenset</h1>
            <p class="intro-text">
                Dieses Kartenset begleitet dich durch die 7 Tage des Co-kreativen Retreats. Jeder Tag hat seinen eigenen Kartenstapel mit speziell ausgewählten Fragen, die dich und deine Gruppe dabei unterstützen, tiefer in die jeweilige Thematik einzutauchen.
                <br><br>
                Wähle den Kartenstapel für den entsprechenden Tag und ziehe eine Karte, um eine inspirierende Frage zu entdecken. Die Fragen sind darauf ausgelegt, Reflexion, Austausch und gemeinsame Erkenntnisse zu fördern.
            </p>
        </div>

        <div class="card-decks">
            <!-- Tag 1: Den Ruf erkunden -->
            <div class="deck deck-tag1" data-category="tag1">
                <h2 class="deck-title">Tag 1: Den Ruf erkunden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                            <div class="deck-label">Den Ruf erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 2: Das Licht erkunden -->
            <div class="deck deck-tag2" data-category="tag2">
                <h2 class="deck-title">Tag 2: Das Licht erkunden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                            <div class="deck-label">Das Licht erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 3: Das gemeinsame Puzzle -->
            <div class="deck deck-tag3" data-category="tag3">
                <h2 class="deck-title">Tag 3: Das gemeinsame Puzzle</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                            <div class="deck-label">Das gemeinsame Puzzle</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 4: Gruppendynamiken und Hürden lösen -->
            <div class="deck deck-tag4" data-category="tag4">
                <h2 class="deck-title">Tag 4: Gruppendynamiken lösen</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                            <div class="deck-label">Gruppendynamiken lösen</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 5: Zum Schwarm werden -->
            <div class="deck deck-tag5" data-category="tag5">
                <h2 class="deck-title">Tag 5: Zum Schwarm werden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                            <div class="deck-label">Zum Schwarm werden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 6: Im Flow -->
            <div class="deck deck-tag6" data-category="tag6">
                <h2 class="deck-title">Tag 6: Im Flow</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                            <div class="deck-label">Im Flow</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 7: Abschluss und Reflexion -->
            <div class="deck deck-tag7" data-category="tag7">
                <h2 class="deck-title">Tag 7: Abschluss & Reflexion</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                            <div class="deck-label">Abschluss & Reflexion</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karten-Modal -->
    <div class="card-modal-wrapper" id="card-modal-wrapper">
        <button class="close-modal" id="close-modal">&times;</button>

        <!-- Kategorie ÜBER dem Modal -->
        <span class="card-category-label" id="card-category-label"></span>

        <div class="modal-card">
            <div class="card-placeholder-container">
                <img src="assets/bilder/Fragen/tag1.png" alt="" class="card-placeholder" id="card-placeholder">
            </div>
            <div class="card-content">
                <p class="card-question-text" id="card-question-text"></p>
            </div>
        </div>

        <!-- Button bleibt unten -->
        <button class="draw-new-card" id="draw-new-card">Neue Karte ziehen</button>
    </div>

    <footer class="footer">
        <p>© 2025 <a href="index.html">Schwarmverbunden</a> | <a href="impressum.html">Impressum & Datenschutz</a></p>
    </footer>

    <!-- Kartenset JavaScript -->
    <script src="kartenset-script.js"></script>
</body>
</html>
