<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retreat Kartenset - Schwarmverbunden</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        @font-face {
            font-family: 'Lora';
            src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }

        @font-face {
            font-family: 'Lora';
            src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }

        @font-face {
            font-family: 'Caveat';
            src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
        }

        @font-face {
            font-family: 'Caveat';
            src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
            font-weight: 700;
            font-style: normal;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lora', serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #29303b;
            color: #fff8ef;
            overflow-x: hidden;
        }
        
        /* Header und Footer */
        .header, .footer {
            background-color: #29303b;
            color: #fbd19a;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            width: 100%;
        }
        
        .header {
            height: 60px;
            font-size: 18px;
            font-family: 'Montserrat', sans-serif;
            position: sticky;
            top: 0;
            z-index: 40;
            border-bottom: 1px solid rgba(251, 209, 154, 0.2);
        }
        
        .header-link {
            color: #fbd19a;
            text-decoration: none;
            font-family: 'Montserrat', sans-serif;
            font-size: 20px;
            font-weight: 300;
            letter-spacing: 1.5px;
            text-transform: uppercase;
        }

        .header-link:hover {
            color: #f59034;
        }
        
        .footer {
            height: 60px;
            font-size: 14px;
            margin-top: auto;
            border-top: 1px solid rgba(251, 209, 154, 0.2);
        }
        
        .footer a {
            color: #fbd19a;
            text-decoration: none;
        }

        .footer a:hover {
            color: #f59034;
        }
        
        /* Container */
        .container {
            flex: 1;
            padding: 40px 20px 60px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        /* Intro Section */
        .intro-section {
            text-align: center;
            margin-bottom: 50px;
            padding: 30px 20px;
            background: linear-gradient(135deg, rgba(251, 201, 154, 0.1), rgba(251, 201, 154, 0.05));
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(251, 201, 154, 0.15);
        }
        
        .main-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 32px;
            font-weight: 600;
            color: #fbd19a;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 15px;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -10px;
            width: 60px;
            height: 3px;
            background-color: #f59034;
            transform: translateX(-50%);
            border-radius: 3px;
        }
        
        .intro-text {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 0;
            color: #fff8ef;
            padding: 0 10px;
        }
        
        /* Kartenstapel-Bereich */
        .card-decks {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 35px;
            margin-bottom: 40px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .deck {
            position: relative;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            perspective: 1000px;
        }
        
        .deck:hover {
            transform: translateY(-8px);
        }
        
        .deck-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: #fbd19a;
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            position: relative;
        }
        
        .deck-title::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            width: 40px;
            height: 2px;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .deck-instruction {
            font-size: 12px;
            color: rgba(255, 248, 239, 0.7);
            text-align: center;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        /* Kategoriespezifische Farben für Titel-Unterstriche */
        .deck-tag1 .deck-title::after { background-color: #f59034; }
        .deck-tag2 .deck-title::after { background-color: #fbd19a; }
        .deck-tag3 .deck-title::after { background-color: #b44355; }
        .deck-tag4 .deck-title::after { background-color: #8b6c5f; }
        .deck-tag5 .deck-title::after { background-color: #f59034; }
        .deck-tag6 .deck-title::after { background-color: #fbd19a; }
        .deck-tag7 .deck-title::after { background-color: #b44355; }
        
        .cards-container {
            position: relative;
            height: 280px;
            perspective: 1200px;
        }
        
        .card {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 15px;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            cursor: pointer;
        }
        
        /* Anpassung für besseren Stapel-Effekt */
        .card.stacked:nth-child(1) { transform: translateZ(0) translateX(0) translateY(0); }
        .card.stacked:nth-child(2) { transform: translateZ(-1px) translateX(2px) translateY(2px); }
        .card.stacked:nth-child(3) { transform: translateZ(-2px) translateX(4px) translateY(4px); }
        .card.stacked:nth-child(4) { transform: translateZ(-3px) translateX(6px) translateY(6px); opacity: 0.9; }
        .card.stacked:nth-child(5) { transform: translateZ(-4px) translateX(8px) translateY(8px); opacity: 0.8; }
        
        /* Deck-Hover-Effekt mit verbesserter Animation */
        .deck:hover .card.stacked:nth-child(1) { 
            transform: translateZ(5px) translateX(0) translateY(-5px); 
            box-shadow: 0 12px 24px rgba(251, 209, 154, 0.2); 
        }
        
        .deck:hover .card.stacked:nth-child(2) { 
            transform: translateZ(3px) translateX(3px) translateY(0px); 
        }
        
        .deck:hover .card.stacked:nth-child(3) { 
            transform: translateZ(1px) translateX(6px) translateY(2px); 
        }
        
        .card-back {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #38160e, #29303b);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            border: 2px solid rgba(251, 209, 154, 0.3);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .card-image-container {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #fbd19a;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(251, 209, 154, 0.1);
        }
        
        .card-cover-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .deck-label {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: #fbd19a;
            text-align: center;
            line-height: 1.3;
        }

        /* Modal Styles */
        .card-modal-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(4, 7, 15, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .card-modal-wrapper.active {
            opacity: 1;
            visibility: visible;
        }

        .close-modal {
            position: absolute;
            top: 30px;
            right: 30px;
            background: none;
            border: none;
            color: #fbd19a;
            font-size: 36px;
            cursor: pointer;
            z-index: 1001;
            transition: color 0.3s ease;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-modal:hover {
            color: #f59034;
        }

        .card-category-label {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: #fbd19a;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .modal-card {
            width: 350px;
            height: 500px;
            background: linear-gradient(135deg, #38160e, #29303b);
            border-radius: 20px;
            border: 3px solid #fbd19a;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: floatIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .card-placeholder-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(251, 209, 154, 0.1);
            border-bottom: 2px solid rgba(251, 209, 154, 0.3);
        }

        .card-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fbd19a;
        }

        .card-content {
            flex: 1;
            padding: 30px 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 300px;
        }

        .card-question-text {
            font-size: 18px;
            line-height: 1.6;
            color: #fff8ef;
            margin: 0;
            font-weight: 400;
        }

        .draw-new-card {
            background: linear-gradient(135deg, #f59034, #b44355);
            color: #fff8ef;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 30px;
            transition: all 0.3s ease;
            font-family: 'Montserrat', sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .draw-new-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 144, 52, 0.4);
        }

        /* Animationen */
        @keyframes floatIn {
            0% {
                opacity: 0;
                transform: scale(0.6) translateY(40px) rotate(-5deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0) rotate(0deg);
            }
        }

        /* Loading Animation */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 4px solid rgba(251, 209, 154, 0.3);
            border-top: 4px solid #fbd19a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 2000;
            display: none;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Deck Animationen */
        .deck {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .deck:nth-child(1) { animation-delay: 0.1s; }
        .deck:nth-child(2) { animation-delay: 0.2s; }
        .deck:nth-child(3) { animation-delay: 0.3s; }
        .deck:nth-child(4) { animation-delay: 0.4s; }
        .deck:nth-child(5) { animation-delay: 0.5s; }
        .deck:nth-child(6) { animation-delay: 0.6s; }
        .deck:nth-child(7) { animation-delay: 0.7s; }

        /* Responsive Anpassungen */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px 40px;
            }

            .card-decks {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
                gap: 25px;
            }

            .cards-container {
                height: 224px;
            }

            .main-title {
                font-size: 24px;
            }

            .modal-card {
                width: 300px;
                height: 450px;
            }

            .card-question-text {
                font-size: 16px;
            }

            .intro-text {
                font-size: 15px;
            }

            .draw-new-card {
                padding: 10px 20px;
                font-size: 14px;
            }

            .intro-section {
                padding: 25px 15px;
                margin-bottom: 30px;
            }

            .close-modal {
                top: 20px;
                right: 20px;
                font-size: 30px;
            }
        }

        @media (max-width: 480px) {
            .card-decks {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .cards-container {
                height: 180px;
            }

            .modal-card {
                width: 280px;
                height: 400px;
            }

            .deck-title {
                font-size: 14px;
                padding-bottom: 8px;
                margin-bottom: 10px;
            }

            .main-title {
                font-size: 20px;
            }

            .card-question-text {
                font-size: 14px;
            }

            .card-placeholder-container {
                height: 140px;
            }

            .card-placeholder {
                width: 80px;
                height: 80px;
            }

            .card-content {
                min-height: 200px;
                padding: 20px 15px;
            }
        }
    </style>
</head>

<body>
    <div class="loading-indicator" id="loader"></div>

    <header class="header">
        <a href="index.html" class="header-link">Schwarmverbunden</a>
    </header>

    <div class="container">
        <div class="intro-section">
            <h1 class="main-title">Retreat Kartenset</h1>
            <p class="intro-text">
                Dieses Kartenset begleitet dich durch die 7 Tage des Co-kreativen Retreats. Jeder Tag hat seinen eigenen Kartenstapel mit speziell ausgewählten Fragen, die dich und deine Gruppe dabei unterstützen, tiefer in die jeweilige Thematik einzutauchen.
                <br><br>
                Wähle den Kartenstapel für den entsprechenden Tag und ziehe eine Karte, um eine inspirierende Frage zu entdecken. Die Fragen sind darauf ausgelegt, Reflexion, Austausch und gemeinsame Erkenntnisse zu fördern.
            </p>
        </div>

        <div class="card-decks">
            <!-- Tag 1: Den Ruf erkunden -->
            <div class="deck deck-tag1" data-category="tag1">
                <h2 class="deck-title">Tag 1: Den Ruf erkunden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                            <div class="deck-label">Den Ruf erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag1.png" alt="Tag 1" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 2: Das Licht erkunden -->
            <div class="deck deck-tag2" data-category="tag2">
                <h2 class="deck-title">Tag 2: Das Licht erkunden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                            <div class="deck-label">Das Licht erkunden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag2.png" alt="Tag 2" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 3: Das gemeinsame Puzzle -->
            <div class="deck deck-tag3" data-category="tag3">
                <h2 class="deck-title">Tag 3: Das gemeinsame Puzzle</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                            <div class="deck-label">Das gemeinsame Puzzle</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag3.png" alt="Tag 3" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 4: Gruppendynamiken und Hürden lösen -->
            <div class="deck deck-tag4" data-category="tag4">
                <h2 class="deck-title">Tag 4: Gruppendynamiken lösen</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                            <div class="deck-label">Gruppendynamiken lösen</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag4.png" alt="Tag 4" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 5: Zum Schwarm werden -->
            <div class="deck deck-tag5" data-category="tag5">
                <h2 class="deck-title">Tag 5: Zum Schwarm werden</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                            <div class="deck-label">Zum Schwarm werden</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag5.png" alt="Tag 5" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 6: Im Flow -->
            <div class="deck deck-tag6" data-category="tag6">
                <h2 class="deck-title">Tag 6: Im Flow</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                            <div class="deck-label">Im Flow</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag6.png" alt="Tag 6" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag 7: Abschluss und Reflexion -->
            <div class="deck deck-tag7" data-category="tag7">
                <h2 class="deck-title">Tag 7: Abschluss & Reflexion</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                            <div class="deck-label">Abschluss & Reflexion</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="assets/bilder/Fragen/tag7.png" alt="Tag 7" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karten-Modal -->
    <div class="card-modal-wrapper" id="card-modal-wrapper">
        <button class="close-modal" id="close-modal">&times;</button>

        <!-- Kategorie ÜBER dem Modal -->
        <span class="card-category-label" id="card-category-label"></span>

        <div class="modal-card">
            <div class="card-placeholder-container">
                <img src="assets/bilder/Fragen/tag1.png" alt="" class="card-placeholder" id="card-placeholder">
            </div>
            <div class="card-content">
                <p class="card-question-text" id="card-question-text"></p>
            </div>
        </div>

        <!-- Button bleibt unten -->
        <button class="draw-new-card" id="draw-new-card">Neue Karte ziehen</button>
    </div>

    <footer class="footer">
        <p>© 2025 <a href="index.html">Schwarmverbunden</a> | <a href="impressum.html">Impressum & Datenschutz</a></p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Loader anzeigen
            const loader = document.getElementById('loader');
            loader.style.display = 'block';

            // Fragen für jede Kategorie
            const questions = {
                tag1: [
                    {
                        text: "Was ist das größte ungestillte Bedürfnis in deinem Leben?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Wenn über Nacht ein Wunder passiert und du findest dich morgen in deinem absoluten Traumleben wieder, was wäre dann anders?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Gibt es aktuelle globale Herausforderungen, die dir besonders Sorgen bereiten?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Gibt es gesellschaftliche oder soziale Themen, die dir besonders wichtig sind?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Welche Werte sind dir wertvoll und wichtig im Leben?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Was bringt dich im Leben am meisten zum Strahlen/Erblühen?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Welche Herausforderungen beschäftigen dich im Moment in deinem Leben am meisten?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Hast du schon mal \"spirituelle Impulse\" zu deiner Vision bekommen (z.B. über dein höheres Selbst, ein Medium, dein Geburtshoroskop o.ä.)?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Welcher Vision würdest du nachgehen, wenn du unendlich viel Selbstvertrauen und Selbstbewusstsein hättest und dich nichts blockieren würde?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Gibt es ein Thema, das dich seit Jahren immer wieder ruft?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    },
                    {
                        text: "Was bedeutet Erfolg für dich ganz persönlich?",
                        placeholder: "assets/bilder/Fragen/tag1.png"
                    }
                ],
                tag2: [
                    {
                        text: "Welche großen Herausforderungen haben dein Leben geprägt und was hast du dabei gelernt?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Gibt es Tätigkeiten, die du lange ausgeübt hast, die du auf keinen Fall wieder machen möchtest?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Was schätzt du an dir selbst, das andere oft übersehen?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Bist du eher ein rationaler Kopfmensch, ein intuitiver Gefühlsmensch oder eher instinktiv-körperlich?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Kennst du deinen Persönlichkeitstyp (z.B. Human Design, 16 Personalities o.ä.) und was macht diesen aus?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Was würdest du gerne können, das du an anderen bewunderst?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Wenn Geld keine Rolle spielen würde und alles erfolgreich wäre, egal wie schräg die Idee ist, womit würdest du dann am liebsten deine Zeit verbringen?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Wo in deinem Leben hast du schon mal jemanden tief inspiriert?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Hast du Gaben, die noch \"roh\" sind und wachsen wollen, die du gerne mal hier in diesem sicheren Rahmen ausprobieren möchtest?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    },
                    {
                        text: "Was machst du regelmäßig, das für dich normal ist, und für andere vielleicht schwer wäre?",
                        placeholder: "assets/bilder/Fragen/tag2.png"
                    }
                ],
                tag3: [
                    {
                        text: "Was möchten wir über die nächsten Tage gemeinsam co-kreieren (Ziel)?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Gibt es ein ungestilltes Bedürfnis in der Gruppe, das wir gemeinsam stillen möchten?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Gibt es ein kreatives Projekt, das wir umsetzen möchten?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Was ist unser \"gemeinsamer Ruf\"?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Wo überschneiden sich unsere Visionen auf kraftvolle Weise?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Welche Fähigkeiten stehen uns als Gruppe zur Verfügung und wie können wir sie schlau einsetzen?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Wer von uns hat einen Platz in welcher Geniezone?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Wie fühlt sich unsere gemeinsame Essenz an, wenn wir sie nur mit einem Wort beschreiben?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Gibt es einen Titel oder Satz, der unsere gemeinsame Ausrichtung beschreibt?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Was haben wir gemeinsam und wo unterscheiden wir uns?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    },
                    {
                        text: "Sind wir als Gruppe zueinander kompatibel (hinsichtlich unserer Werte, Visionen, Lebensvorstellungen)?",
                        placeholder: "assets/bilder/Fragen/tag3.png"
                    }
                ],
                tag4: [
                    {
                        text: "Welche Schwierigkeiten hast du generell in Gruppen und ist das in dieser Gruppe schon aufgetreten?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Wo waren wir die letzten Tage irgendwie nicht so richtig im Flow und woran könnte es gelegen haben?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Gibt es Momente, in denen du dich nicht so richtig verstanden gefühlt hast, und möchtest du deine Perspektive noch einmal ganz ausführlich erklären?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Welche Hürden könnten dem Erreichen unseres Ziels im Wege stehen?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Welche (noch unausgesprochenen) Erwartungen hast du an die Gruppe oder unser Projekt?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Welche gesellschaftlichen Rollen und Erwartungen engen dich ein?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Was in uns darf sich verändern, damit dieses Projekt gelingen kann?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Gibt es etwas, das du ansprechen/aussprechen möchtest, aber dich bisher nicht getraut hast?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Gibt es Gefühle, die du bisher zurückgehalten hast, denen wir gemeinsam Raum geben können?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "In welchen Momenten hast du dich in der Gruppe besonders verbunden gefühlt?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Gibt es etwas, das wir tun können, damit du dich noch sicherer/freier fühlen kannst, du selbst zu sein?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Worin möchtest du der Gruppe (noch) mehr vertrauen?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    },
                    {
                        text: "Was brauchst du, um dich noch mehr einlassen zu können?",
                        placeholder: "assets/bilder/Fragen/tag4.png"
                    }
                ],
                tag5: [
                    {
                        text: "Wie wollen wir jetzt vorgehen?",
                        placeholder: "assets/bilder/Fragen/tag5.png"
                    },
                    {
                        text: "Auf welche Weise könnten wir unser Ziel angehen, die besonders viel Spaß macht?",
                        placeholder: "assets/bilder/Fragen/tag5.png"
                    },
                    {
                        text: "Angenommen, jeder macht nur das, was leicht fällt, was würde dann passieren?",
                        placeholder: "assets/bilder/Fragen/tag5.png"
                    },
                    {
                        text: "Gibt es innere Impulse, die wir spüren, denen wir nachgehen könnten?",
                        placeholder: "assets/bilder/Fragen/tag5.png"
                    },
                    {
                        text: "Welche Strukturen würden unserem Schwarm gut tun?",
                        placeholder: "assets/bilder/Fragen/tag5.png"
                    }
                ],
                tag6: [
                    {
                        text: "Gibt es etwas, das wir heute anders machen möchten, als gestern?",
                        placeholder: "assets/bilder/Fragen/tag6.png"
                    },
                    {
                        text: "Haben wir bisher eine Schwarmintelligenz wahrgenommen? Wie können wir darauf stärker zugreifen?",
                        placeholder: "assets/bilder/Fragen/tag6.png"
                    },
                    {
                        text: "Welche Richtung würde unser Projekt heute nehmen, wenn wir etwas Mutiges tun würden?",
                        placeholder: "assets/bilder/Fragen/tag6.png"
                    }
                ],
                tag7: [
                    {
                        text: "Wie gut ist es uns gelungen, zu co-kreieren?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Wie hat sich die Gruppendynamik über die Zeit entwickelt?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Was würdest du nächstes Mal anders machen?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Was braucht es noch, um mit dem Projekt einen guten Abschluss zu finden?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Wollen wir das Projekt auch nach dem Retreat weiterführen und was braucht es dafür noch?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Wie hast du dich die letzten Tage gefühlt?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Was war dein größter innerer Shift über die Woche?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Was möchtest du aus dieser Erfahrung in dein Leben mitnehmen?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Wofür bist du dankbar?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Was möchtest du der Gruppe gerne rückmelden?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    },
                    {
                        text: "Gibt es etwas, das noch ausgesprochen werden möchte?",
                        placeholder: "assets/bilder/Fragen/tag7.png"
                    }
                ]
            };

            // DOM-Elemente
            const decks = document.querySelectorAll('.deck');
            const cardModalWrapper = document.getElementById('card-modal-wrapper');
            const closeModalBtn = document.getElementById('close-modal');
            const drawNewCardBtn = document.getElementById('draw-new-card');
            const cardQuestionText = document.getElementById('card-question-text');
            const cardPlaceholder = document.getElementById('card-placeholder');
            const cardCategoryLabel = document.getElementById('card-category-label');
            const modalCard = document.querySelector('.modal-card');

            let activeCategory = null;
            let currentDeck = null;

            // Kategorie-Labels
            const categoryLabels = {
                tag1: "Tag 1: Den Ruf erkunden",
                tag2: "Tag 2: Das Licht erkunden",
                tag3: "Tag 3: Das gemeinsame Puzzle",
                tag4: "Tag 4: Gruppendynamiken lösen",
                tag5: "Tag 5: Zum Schwarm werden",
                tag6: "Tag 6: Im Flow",
                tag7: "Tag 7: Abschluss & Reflexion"
            };

            // Funktion um eine zufällige Karte zu ziehen mit Animation
            function drawCard(category, deck) {
                // Lade-Animation anzeigen
                loader.style.display = 'block';

                // Aktive Kategorie und Deck speichern
                activeCategory = category;
                currentDeck = deck;

                // Deck-Position für Animation speichern
                let deckRect = null;
                if (deck) {
                    deckRect = deck.getBoundingClientRect();
                }

                setTimeout(() => {
                    // Zufällige Frage aus der Kategorie wählen
                    const categoryQuestions = questions[category];
                    const randomQuestion = categoryQuestions[Math.floor(Math.random() * categoryQuestions.length)];

                    // Modal-Inhalte setzen
                    cardQuestionText.textContent = randomQuestion.text;
                    cardPlaceholder.src = randomQuestion.placeholder;
                    cardCategoryLabel.textContent = categoryLabels[category];

                    // Modal anzeigen
                    cardModalWrapper.classList.add('active');

                    // Karten-Zieh-Animation
                    if (deckRect) {
                        // Setze die Modalcard für die Animation zurück
                        modalCard.style.animation = 'none';
                        modalCard.style.opacity = '0';
                        modalCard.style.transform = 'scale(0.6) translateY(40px) rotate(-5deg)';

                        // Kleine Verzögerung für visuelle Effekte
                        setTimeout(() => {
                            // Animation starten
                            modalCard.style.animation = 'floatIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';
                        }, 100);
                    }

                    // Lade-Animation ausblenden
                    loader.style.display = 'none';
                }, 300);
            }

            // Event-Listener für Kartenstapel
            decks.forEach(deck => {
                deck.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    drawCard(category, this);
                });
            });

            // Event-Listener für Modal schließen
            closeModalBtn.addEventListener('click', function() {
                cardModalWrapper.classList.remove('active');
                setTimeout(() => {
                    activeCategory = null;
                    currentDeck = null;
                }, 300);
            });

            // Event-Listener für "Neue Karte ziehen"
            drawNewCardBtn.addEventListener('click', function() {
                if (activeCategory) {
                    // Für die neue Karte eine kleine Animation
                    modalCard.style.animation = 'none';
                    modalCard.offsetHeight; // Reflow
                    modalCard.style.animation = 'floatIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';

                    // Neue Karte aus der gleichen Kategorie ziehen
                    drawCard(activeCategory, currentDeck);
                }
            });

            // ESC-Taste zum Schließen
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && cardModalWrapper.classList.contains('active')) {
                    cardModalWrapper.classList.remove('active');
                    setTimeout(() => {
                        activeCategory = null;
                        currentDeck = null;
                    }, 300);
                }
            });

            // Modal-Wrapper Click zum Schließen
            cardModalWrapper.addEventListener('click', function(e) {
                if (e.target === cardModalWrapper) {
                    cardModalWrapper.classList.remove('active');
                    setTimeout(() => {
                        activeCategory = null;
                        currentDeck = null;
                    }, 300);
                }
            });

            // Bilder vorladen
            function preloadImages() {
                const imageUrls = [
                    'assets/bilder/Fragen/tag1.png',
                    'assets/bilder/Fragen/tag2.png',
                    'assets/bilder/Fragen/tag3.png',
                    'assets/bilder/Fragen/tag4.png',
                    'assets/bilder/Fragen/tag5.png',
                    'assets/bilder/Fragen/tag6.png',
                    'assets/bilder/Fragen/tag7.png'
                ];

                const imagePromises = imageUrls.map(url => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        img.onload = () => resolve(url);
                        img.onerror = () => {
                            console.warn(`Konnte Bild nicht laden: ${url}`);
                            resolve(url); // Trotzdem auflösen, damit der Prozess weitergeht
                        };
                        img.src = url;
                    });
                });

                return Promise.all(imagePromises);
            }

            // Initialisierung
            preloadImages().then(() => {
                // Loader ausblenden nach dem Laden
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 500);
            });
        });
    </script>
</body>
</html>
